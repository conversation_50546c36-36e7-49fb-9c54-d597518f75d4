#!/bin/bash

# iOS构建脚本 - 生成.framework文件并静态链接OpenCV
# 使用方法: ./build_ios.sh [平台]
# 支持的平台: OS64 (设备), SIMULATOR64 (模拟器), UNIVERSAL (通用)

set -e

# 颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 默认配置
DEFAULT_PLATFORM="UNIVERSAL"
DEFAULT_DEPLOYMENT_TARGET="11.0"

# 检查参数
PLATFORM=${1:-$DEFAULT_PLATFORM}
DEPLOYMENT_TARGET=${2:-$DEFAULT_DEPLOYMENT_TARGET}

echo -e "${GREEN}开始构建iOS .framework文件${NC}"
echo -e "${YELLOW}目标平台: $PLATFORM${NC}"
echo -e "${YELLOW}部署目标: $DEPLOYMENT_TARGET${NC}"

# 检查Xcode
if ! command -v xcodebuild &> /dev/null; then
    echo -e "${RED}错误: 未找到Xcode，请确保已安装Xcode${NC}"
    exit 1
fi

# 检查OpenCV
if [ -z "$OPENCV_IOS_SDK" ]; then
    echo -e "${RED}错误: 请设置OPENCV_IOS_SDK环境变量${NC}"
    echo "例如: export OPENCV_IOS_SDK=/path/to/opencv2.framework"
    exit 1
fi

if [ ! -d "$OPENCV_IOS_SDK" ]; then
    echo -e "${RED}错误: OpenCV iOS SDK路径不存在: $OPENCV_IOS_SDK${NC}"
    exit 1
fi

# 项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
IOS_TOOLCHAIN="$PROJECT_ROOT/ios/ios.toolchain.cmake"
OUTPUT_DIR="$PROJECT_ROOT/ios/frameworks"

echo -e "${YELLOW}项目根目录: $PROJECT_ROOT${NC}"
echo -e "${YELLOW}输出目录: $OUTPUT_DIR${NC}"

# 创建输出目录
mkdir -p "$OUTPUT_DIR"

# 构建函数
build_for_platform() {
    local platform=$1
    local build_dir="$PROJECT_ROOT/build_ios_$platform"
    
    echo -e "${GREEN}构建平台: $platform${NC}"
    
    # 清理并创建构建目录
    rm -rf "$build_dir"
    mkdir -p "$build_dir"
    cd "$build_dir"
    
    # 配置CMake
    cmake \
        -DCMAKE_TOOLCHAIN_FILE="$IOS_TOOLCHAIN" \
        -DPLATFORM="$platform" \
        -DDEPLOYMENT_TARGET="$DEPLOYMENT_TARGET" \
        -DCMAKE_BUILD_TYPE=Release \
        -DOPENCV_IOS_SDK="$OPENCV_IOS_SDK" \
        -DSTATIC_OPENCV=ON \
        -DIOS_FRAMEWORK=ON \
        -DCMAKE_INSTALL_PREFIX="$build_dir/install" \
        "$PROJECT_ROOT"
    
    # 编译
    cmake --build . --config Release -j$(sysctl -n hw.ncpu)
    
    # 安装
    cmake --install . --config Release
    
    echo -e "${GREEN}平台 $platform 构建完成${NC}"
}

# 根据平台参数构建
case $PLATFORM in
    "OS64")
        build_for_platform "OS64"
        cp -R "build_ios_OS64/install/Frameworks/image_blend_ffi.framework" "$OUTPUT_DIR/"
        ;;
    "SIMULATOR64")
        build_for_platform "SIMULATOR64"
        cp -R "build_ios_SIMULATOR64/install/Frameworks/image_blend_ffi.framework" "$OUTPUT_DIR/"
        ;;
    "UNIVERSAL")
        echo -e "${GREEN}构建通用framework...${NC}"
        
        # 构建设备版本
        build_for_platform "OS64"
        
        # 构建模拟器版本
        build_for_platform "SIMULATOR64"
        
        # 创建通用framework
        DEVICE_FRAMEWORK="build_ios_OS64/install/Frameworks/image_blend_ffi.framework"
        SIMULATOR_FRAMEWORK="build_ios_SIMULATOR64/install/Frameworks/image_blend_ffi.framework"
        UNIVERSAL_FRAMEWORK="$OUTPUT_DIR/image_blend_ffi.framework"
        
        # 复制framework结构
        cp -R "$DEVICE_FRAMEWORK" "$UNIVERSAL_FRAMEWORK"
        
        # 合并二进制文件
        lipo -create \
            "$DEVICE_FRAMEWORK/image_blend_ffi" \
            "$SIMULATOR_FRAMEWORK/image_blend_ffi" \
            -output "$UNIVERSAL_FRAMEWORK/image_blend_ffi"
        
        echo -e "${GREEN}通用framework创建完成${NC}"
        ;;
    *)
        echo -e "${RED}错误: 不支持的平台 $PLATFORM${NC}"
        echo "支持的平台: OS64, SIMULATOR64, UNIVERSAL"
        exit 1
        ;;
esac

# 检查生成的framework
if [ -d "$OUTPUT_DIR/image_blend_ffi.framework" ]; then
    echo -e "${GREEN}构建成功!${NC}"
    
    # 显示framework信息
    echo -e "${GREEN}生成的framework信息:${NC}"
    ls -la "$OUTPUT_DIR/image_blend_ffi.framework/"
    
    # 检查架构
    echo -e "${GREEN}支持的架构:${NC}"
    lipo -info "$OUTPUT_DIR/image_blend_ffi.framework/image_blend_ffi"
    
    # 检查依赖
    echo -e "${GREEN}依赖检查:${NC}"
    otool -L "$OUTPUT_DIR/image_blend_ffi.framework/image_blend_ffi" || true
    
else
    echo -e "${RED}构建失败: 未找到image_blend_ffi.framework${NC}"
    exit 1
fi

echo -e "${GREEN}iOS构建完成!${NC}"
echo -e "${YELLOW}输出文件: $OUTPUT_DIR/image_blend_ffi.framework${NC}"
