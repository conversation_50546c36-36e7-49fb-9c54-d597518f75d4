# iOS CMake工具链文件
# 基于 https://github.com/leetal/ios-cmake

cmake_minimum_required(VERSION 3.16)

# 设置系统名称
set(CMAKE_SYSTEM_NAME iOS)

# 平台选项
set(PLATFORM "OS64" CACHE STRING "iOS平台")
set_property(CACHE PLATFORM PROPERTY STRINGS "OS64" "SIMULATOR64" "UNIVERSAL")

# 部署目标
set(DEPLOYMENT_TARGET "11.0" CACHE STRING "iOS部署目标版本")

# 根据平台设置架构
if(PLATFORM STREQUAL "OS64")
    # 真机设备 (iPhone/iPad)
    set(CMAKE_OSX_ARCHITECTURES "arm64")
    set(CMAKE_SYSTEM_PROCESSOR "arm64")
    set(CMAKE_OSX_SYSROOT "iphoneos")
elseif(PLATFORM STREQUAL "SIMULATOR64")
    # 模拟器 (支持 Intel Mac 和 Apple Silicon Mac)
    set(CMAKE_OSX_ARCHITECTURES "x86_64;arm64")
    set(CMAKE_SYSTEM_PROCESSOR "x86_64")
    set(CMAKE_OSX_SYSROOT "iphonesimulator")
elseif(PLATFORM STREQUAL "UNIVERSAL")
    # 通用版本 (这个在脚本中处理，这里设置为设备版本)
    set(CMAKE_OSX_ARCHITECTURES "arm64")
    set(CMAKE_SYSTEM_PROCESSOR "arm64")
    set(CMAKE_OSX_SYSROOT "iphoneos")
else()
    message(FATAL_ERROR "不支持的平台: ${PLATFORM}")
endif()

# 设置部署目标
set(CMAKE_OSX_DEPLOYMENT_TARGET ${DEPLOYMENT_TARGET})

# 查找开发者目录
execute_process(
    COMMAND xcode-select -print-path
    OUTPUT_VARIABLE XCODE_DEVELOPER_DIR
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

# 设置SDK路径
execute_process(
    COMMAND xcrun --sdk ${CMAKE_OSX_SYSROOT} --show-sdk-path
    OUTPUT_VARIABLE CMAKE_OSX_SYSROOT_PATH
    OUTPUT_STRIP_TRAILING_WHITESPACE
)
set(CMAKE_OSX_SYSROOT ${CMAKE_OSX_SYSROOT_PATH})

# 设置编译器
set(CMAKE_C_COMPILER ${XCODE_DEVELOPER_DIR}/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang)
set(CMAKE_CXX_COMPILER ${XCODE_DEVELOPER_DIR}/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang++)

# 编译器标志 - 修复符号可见性问题
set(CMAKE_C_FLAGS_INIT "-fvisibility=default")
set(CMAKE_CXX_FLAGS_INIT "-fvisibility=default")

# 链接器标志
set(CMAKE_SHARED_LINKER_FLAGS_INIT "-Wl,-undefined,dynamic_lookup")
set(CMAKE_MODULE_LINKER_FLAGS_INIT "-Wl,-undefined,dynamic_lookup")

# 设置某些变量
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)

# 设置查找路径
set(CMAKE_FIND_ROOT_PATH ${CMAKE_OSX_SYSROOT})

# 强制使用静态库
set(CMAKE_FIND_LIBRARY_SUFFIXES ".a")

# 设置Xcode属性
set(CMAKE_XCODE_ATTRIBUTE_DEVELOPMENT_TEAM "" CACHE STRING "开发团队ID")
set(CMAKE_XCODE_ATTRIBUTE_CODE_SIGN_IDENTITY "iPhone Developer" CACHE STRING "代码签名身份")
set(CMAKE_XCODE_ATTRIBUTE_IPHONEOS_DEPLOYMENT_TARGET ${DEPLOYMENT_TARGET})
set(CMAKE_XCODE_ATTRIBUTE_ENABLE_BITCODE "NO")
set(CMAKE_XCODE_ATTRIBUTE_ONLY_ACTIVE_ARCH "NO")

# 设置framework相关属性
set(CMAKE_XCODE_ATTRIBUTE_DEFINES_MODULE "YES")
set(CMAKE_XCODE_ATTRIBUTE_MODULEMAP_FILE "")
set(CMAKE_XCODE_ATTRIBUTE_PRODUCT_BUNDLE_IDENTIFIER "com.example.imageblend")

# 符号导出设置
set(CMAKE_XCODE_ATTRIBUTE_GCC_SYMBOLS_PRIVATE_EXTERN "NO")
set(CMAKE_XCODE_ATTRIBUTE_GCC_INLINES_ARE_PRIVATE_EXTERN "NO")

# 辅助函数：设置iOS framework属性
function(set_ios_framework_properties target)
    set_target_properties(${target} PROPERTIES
        FRAMEWORK TRUE
        MACOSX_FRAMEWORK_IDENTIFIER "com.example.imageblend"
        MACOSX_FRAMEWORK_BUNDLE_VERSION "1.0"
        MACOSX_FRAMEWORK_SHORT_VERSION_STRING "1.0"
        PUBLIC_HEADER "${CMAKE_SOURCE_DIR}/src/image_blend_ffi/image_blend_ffi.h"
        # 确保符号导出
        CXX_VISIBILITY_PRESET default
        VISIBILITY_INLINES_HIDDEN OFF
    )
    
    # 设置framework的Info.plist
    set_target_properties(${target} PROPERTIES
        MACOSX_FRAMEWORK_INFO_PLIST "${CMAKE_SOURCE_DIR}/ios/Info.plist"
    )
endfunction()

# 信息输出
message(STATUS "iOS构建配置:")
message(STATUS "  平台: ${PLATFORM}")
message(STATUS "  架构: ${CMAKE_OSX_ARCHITECTURES}")
message(STATUS "  SDK: ${CMAKE_OSX_SYSROOT}")
message(STATUS "  部署目标: ${CMAKE_OSX_DEPLOYMENT_TARGET}")
message(STATUS "  编译器: ${CMAKE_CXX_COMPILER}")
