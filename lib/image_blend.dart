/// 图像混合库的主入口文件
///
/// 这个库提供了完整的图像混合功能，包括：
/// - 图像叠加（支持蒙版）
/// - 图像平铺
/// - 图像裁剪
/// - 缓存管理
///
/// 支持两种异步执行方式：
/// - compute: 适用于 Flutter UI，轻量级后台处理
/// - Isolate: 适用于长时间运行的任务，完全隔离的后台处理
library image_blend;

// 导出所有公共接口
export 'models/blend_models.dart';
export 'services/image_blend_service.dart';
export 'image_blend_ffi.dart' show ImageBlendFFI, ImageBlendAsyncHelper;

// 便捷的全局访问实例
import 'models/blend_models.dart';
import 'services/image_blend_service.dart';

/// 全局图像混合服务实例
final ImageBlendService imageBlend = ImageBlendService();

/// 便捷的全局函数

/// 图像叠加 - 使用 compute
Future<FFIResult> overlayImages({
  required String baseImagePath,
  required String topImagePath,
  required String outputPath,
  BlendMode blendMode = BlendMode.normal,
  TilingMode tilingMode = TilingMode.stretch,
  int opacity = 100,
  int tilingScale = 100,
  String? maskImagePath,
}) {
  return imageBlend.overlayImages(
    baseImagePath: baseImagePath,
    topImagePath: topImagePath,
    outputPath: outputPath,
    blendMode: blendMode,
    tilingMode: tilingMode,
    opacity: opacity,
    tilingScale: tilingScale,
    maskImagePath: maskImagePath,
  );
}

/// 图像叠加 - 使用 Isolate
Future<FFIResult> overlayImagesIsolate({
  required String baseImagePath,
  required String topImagePath,
  required String outputPath,
  BlendMode blendMode = BlendMode.normal,
  TilingMode tilingMode = TilingMode.stretch,
  int opacity = 100,
  int tilingScale = 100,
  String? maskImagePath,
}) {
  return imageBlend.overlayImagesWithIsolate(
    baseImagePath: baseImagePath,
    topImagePath: topImagePath,
    outputPath: outputPath,
    blendMode: blendMode,
    tilingMode: tilingMode,
    opacity: opacity,
    tilingScale: tilingScale,
    maskImagePath: maskImagePath,
  );
}

/// 图像平铺 - 使用 compute
Future<FFIResult> tileImage({
  required String imagePath,
  required String outputPath,
  int tileMultiplier = 2,
}) {
  return imageBlend.tileImage(
    imagePath: imagePath,
    outputPath: outputPath,
    tileMultiplier: tileMultiplier,
  );
}

/// 图像平铺 - 使用 Isolate
Future<FFIResult> tileImageIsolate({
  required String imagePath,
  required String outputPath,
  int tileMultiplier = 2,
}) {
  return imageBlend.tileImageWithIsolate(
    imagePath: imagePath,
    outputPath: outputPath,
    tileMultiplier: tileMultiplier,
  );
}

/// 图像裁剪 - 使用 compute
Future<FFIResult> trimImage({
  required String inputPath,
  required String outputPath,
}) {
  return imageBlend.trimImage(inputPath: inputPath, outputPath: outputPath);
}

/// 图像裁剪 - 使用 Isolate
Future<FFIResult> trimImageIsolate({
  required String inputPath,
  required String outputPath,
}) {
  return imageBlend.trimImageWithIsolate(
    inputPath: inputPath,
    outputPath: outputPath,
  );
}

/// 清理缓存 - 使用 compute
Future<FFIResult> clearCache() {
  return imageBlend.clearCache();
}

/// 清理缓存 - 使用 Isolate
Future<FFIResult> clearCacheIsolate() {
  return imageBlend.clearCacheWithIsolate();
}

/// 批量处理图像叠加
Future<List<FFIResult>> batchOverlayImages(
  List<OverlayImageParams> paramsList, {
  bool useIsolate = false,
}) {
  return imageBlend.batchOverlayImages(paramsList, useIsolate: useIsolate);
}
