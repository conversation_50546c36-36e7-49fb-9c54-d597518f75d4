/// ͼ���� FFI �󶨺͵ײ�ӿ�
library image_blend_ffi;

import 'dart:ffi';
import 'dart:io';
import 'dart:isolate';
import 'package:ffi/ffi.dart';
import 'models/blend_models.dart';

/// FFI ����ǩ������

/// overlay_images ����ǩ��
typedef OverlayImagesNative = Int32 Function(
  Pointer<Utf8> baseImagePath,
  Pointer<Utf8> topImagePath,
  Int32 blendMode,
  Int32 tilingMode,
  Int32 opacity,
  Pointer<Utf8> outputPath,
  Int32 tilingScale,
  Pointer<Utf8> maskImagePath,
);

typedef OverlayImagesDart = int Function(
  Pointer<Utf8> baseImagePath,
  Pointer<Utf8> topImagePath,
  int blendMode,
  int tilingMode,
  int opacity,
  Pointer<Utf8> outputPath,
  int tilingScale,
  Pointer<Utf8> maskImagePath,
);

/// tile_image ����ǩ��
typedef TileImageNative = Int32 Function(
  Pointer<Utf8> imagePath,
  Int32 tileMultiplier,
  Pointer<Utf8> outputPath,
);

typedef TileImageDart = int Function(
  Pointer<Utf8> imagePath,
  int tileMultiplier,
  Pointer<Utf8> outputPath,
);

/// trim_image ����ǩ��
typedef TrimImageNative = Int32 Function(
  Pointer<Utf8> inputPath,
  Pointer<Utf8> outputPath,
);

typedef TrimImageDart = int Function(
  Pointer<Utf8> inputPath,
  Pointer<Utf8> outputPath,
);

/// clear_cache ����ǩ��
typedef ClearCacheNative = Int32 Function();
typedef ClearCacheDart = int Function();

/// ͼ���� FFI ����
class ImageBlendFFI {
  static ImageBlendFFI? _instance;
  late final DynamicLibrary _library;
  late final OverlayImagesDart _overlayImages;
  late final TileImageDart _tileImage;
  late final TrimImageDart _trimImage;
  late final ClearCacheDart _clearCache;

  /// ����ģʽ
  factory ImageBlendFFI() {
    return _instance ??= ImageBlendFFI._internal();
  }

  ImageBlendFFI._internal() {
    _loadLibrary();
    _bindFunctions();
  }

  /// ���ض�̬��
  void _loadLibrary() {
    if (Platform.isAndroid) {
      _library = DynamicLibrary.open('libimage_blend_ffi.so');
    } else if (Platform.isIOS) {
      // iOS 使用 executable() 来访问嵌入的 framework
      _library = DynamicLibrary.executable();
    } else {
      throw UnsupportedError('��֧�ֵ�ƽ̨: ${Platform.operatingSystem}');
    }
  }

  /// �� FFI ����
  void _bindFunctions() {
    _overlayImages = _library
        .lookup<NativeFunction<OverlayImagesNative>>('overlay_images')
        .asFunction();

    _tileImage = _library
        .lookup<NativeFunction<TileImageNative>>('tile_image')
        .asFunction();

    _trimImage = _library
        .lookup<NativeFunction<TrimImageNative>>('trim_image')
        .asFunction();

    _clearCache = _library
        .lookup<NativeFunction<ClearCacheNative>>('clear_cache')
        .asFunction();
  }

  /// ͬ������ͼ����Ӻ���
  int overlayImagesSync(OverlayImageParams params) {
    final basePathPtr = params.baseImagePath.toNativeUtf8();
    final topPathPtr = params.topImagePath.toNativeUtf8();
    final outputPathPtr = params.outputPath.toNativeUtf8();
    final maskPathPtr = params.maskImagePath?.toNativeUtf8() ?? nullptr;

    try {
      return _overlayImages(
        basePathPtr,
        topPathPtr,
        params.blendMode.value,
        params.tilingMode.value,
        params.opacity,
        outputPathPtr,
        params.tilingScale,
        maskPathPtr,
      );
    } finally {
      malloc.free(basePathPtr);
      malloc.free(topPathPtr);
      malloc.free(outputPathPtr);
      if (maskPathPtr != nullptr) {
        malloc.free(maskPathPtr);
      }
    }
  }

  /// ͬ������ͼ��ƽ�̺���
  int tileImageSync(TileImageParams params) {
    final imagePathPtr = params.imagePath.toNativeUtf8();
    final outputPathPtr = params.outputPath.toNativeUtf8();

    try {
      return _tileImage(
        imagePathPtr,
        params.tileMultiplier,
        outputPathPtr,
      );
    } finally {
      malloc.free(imagePathPtr);
      malloc.free(outputPathPtr);
    }
  }

  /// ͬ������ͼ��ü�����
  int trimImageSync(TrimImageParams params) {
    final inputPathPtr = params.inputPath.toNativeUtf8();
    final outputPathPtr = params.outputPath.toNativeUtf8();

    try {
      return _trimImage(inputPathPtr, outputPathPtr);
    } finally {
      malloc.free(inputPathPtr);
      malloc.free(outputPathPtr);
    }
  }

  /// ͬ�������������溯��
  int clearCacheSync() {
    return _clearCache();
  }
}

/// Isolate ��ִ�еĺ���

/// �� Isolate ��ִ��ͼ�����
static void _overlayImagesIsolate(SendPort sendPort) async {
  final receivePort = ReceivePort();
  sendPort.send(receivePort.sendPort);

  await for (final message in receivePort) {
    if (message is OverlayImageParams) {
      try {
        final ffi = ImageBlendFFI();
        final result = ffi.overlayImagesSync(message);
        sendPort.send(FFIResult(errorCode: result));
      } catch (e) {
        sendPort.send(FFIResult.error(FFIErrorCodes.processingFailed, e.toString()));
      }
    } else if (message == 'close') {
      receivePort.close();
      break;
    }
  }
}

/// �� Isolate ��ִ��ͼ��ƽ��
static void _tileImageIsolate(SendPort sendPort) async {
  final receivePort = ReceivePort();
  sendPort.send(receivePort.sendPort);

  await for (final message in receivePort) {
    if (message is TileImageParams) {
      try {
        final ffi = ImageBlendFFI();
        final result = ffi.tileImageSync(message);
        sendPort.send(FFIResult(errorCode: result));
      } catch (e) {
        sendPort.send(FFIResult.error(FFIErrorCodes.processingFailed, e.toString()));
      }
    } else if (message == 'close') {
      receivePort.close();
      break;
    }
  }
}

/// �� Isolate ��ִ��ͼ��ü�
static void _trimImageIsolate(SendPort sendPort) async {
  final receivePort = ReceivePort();
  sendPort.send(receivePort.sendPort);

  await for (final message in receivePort) {
    if (message is TrimImageParams) {
      try {
        final ffi = ImageBlendFFI();
        final result = ffi.trimImageSync(message);
        sendPort.send(FFIResult(errorCode: result));
      } catch (e) {
        sendPort.send(FFIResult.error(FFIErrorCodes.processingFailed, e.toString()));
      }
    } else if (message == 'close') {
      receivePort.close();
      break;
    }
  }
}

/// �� Isolate ��ִ����������
static void _clearCacheIsolate(SendPort sendPort) async {
  final receivePort = ReceivePort();
  sendPort.send(receivePort.sendPort);

  await for (final message in receivePort) {
    if (message == 'clear') {
      try {
        final ffi = ImageBlendFFI();
        final result = ffi.clearCacheSync();
        sendPort.send(FFIResult(errorCode: result));
      } catch (e) {
        sendPort.send(FFIResult.error(FFIErrorCodes.processingFailed, e.toString()));
      }
    } else if (message == 'close') {
      receivePort.close();
      break;
    }
  }
}

/// �첽 FFI ����������
class ImageBlendAsyncHelper {
  /// ʹ�� Isolate ִ��ͼ����Ӳ���
  static Future<FFIResult> overlayImagesAsync(OverlayImageParams params) async {
    if (!params.isValid()) {
      return FFIResult.error(FFIErrorCodes.invalidParams, '������֤ʧ��');
    }

    final receivePort = ReceivePort();
    final isolate = await Isolate.spawn(_overlayImagesIsolate, receivePort.sendPort);

    final sendPort = await receivePort.first as SendPort;
    final resultPort = ReceivePort();

    sendPort.send(params);
    final result = await resultPort.first as FFIResult;

    sendPort.send('close');
    isolate.kill();
    receivePort.close();
    resultPort.close();

    return result;
  }

  /// ʹ�� Isolate ִ��ͼ��ƽ�̲���
  static Future<FFIResult> tileImageAsync(TileImageParams params) async {
    if (!params.isValid()) {
      return FFIResult.error(FFIErrorCodes.invalidParams, '������֤ʧ��');
    }

    final receivePort = ReceivePort();
    final isolate = await Isolate.spawn(_tileImageIsolate, receivePort.sendPort);

    final sendPort = await receivePort.first as SendPort;
    final resultPort = ReceivePort();

    sendPort.send(params);
    final result = await resultPort.first as FFIResult;

    sendPort.send('close');
    isolate.kill();
    receivePort.close();
    resultPort.close();

    return result;
  }

  /// ʹ�� Isolate ִ��ͼ��ü�����
  static Future<FFIResult> trimImageAsync(TrimImageParams params) async {
    if (!params.isValid()) {
      return FFIResult.error(FFIErrorCodes.invalidParams, '������֤ʧ��');
    }

    final receivePort = ReceivePort();
    final isolate = await Isolate.spawn(_trimImageIsolate, receivePort.sendPort);

    final sendPort = await receivePort.first as SendPort;
    final resultPort = ReceivePort();

    sendPort.send(params);
    final result = await resultPort.first as FFIResult;

    sendPort.send('close');
    isolate.kill();
    receivePort.close();
    resultPort.close();

    return result;
  }

  /// ʹ�� Isolate ִ�������������
  static Future<FFIResult> clearCacheAsync() async {
    final receivePort = ReceivePort();
    final isolate = await Isolate.spawn(_clearCacheIsolate, receivePort.sendPort);

    final sendPort = await receivePort.first as SendPort;
    final resultPort = ReceivePort();

    sendPort.send('clear');
    final result = await resultPort.first as FFIResult;

    sendPort.send('close');
    isolate.kill();
    receivePort.close();
    resultPort.close();

    return result;
  }
}
