#ifndef IMAGE_BLEND_FFI_H
#define IMAGE_BLEND_FFI_H

// 符号导出宏定义
#ifdef __cplusplus
#define FFI_EXPORT extern "C" __attribute__((visibility("default")))
#else
#define FFI_EXPORT __attribute__((visibility("default")))
#endif

#ifdef __cplusplus
extern "C"
{
#endif

// ????????
#define FFI_SUCCESS 0
#define FFI_ERROR_INVALID_PARAMS -1
#define FFI_ERROR_FILE_NOT_FOUND -2
#define FFI_ERROR_PROCESSING_FAILED -3
#define FFI_ERROR_OUT_OF_MEMORY -4

    // ????????
    typedef enum
    {
        BLEND_MODE_NORMAL = 0,
        BLEND_MODE_MULTIPLY = 1,
        BLEND_MODE_OVERLAY = 2,
        BLEND_MODE_SOFT_LIGHT = 3,
        <PERSON><PERSON><PERSON>_MODE_SCREEN = 4
    } BlendMode;

    // ????????
    typedef enum
    {
        TILING_MODE_STRETCH = 0,
        TILING_MODE_TILE = 1
    } TilingMode;

    /**
     * Overlay images with optional mask support
     * @param base_image_path Base image path
     * @param top_image_path Top image path
     * @param blend_mode Blend mode (BlendMode enum value)
     * @param tiling_mode Tiling mode (TilingMode enum value)
     * @param opacity Opacity (0-100)
     * @param output_path Output path
     * @param tiling_scale Tiling scale (1-1000)
     * @param mask_image_path Mask image path (optional, pass NULL for no mask)
     * @return Error code, FFI_SUCCESS indicates success
     */
    FFI_EXPORT int overlay_images(
        const char *base_image_path,
        const char *top_image_path,
        int blend_mode,
        int tiling_mode,
        int opacity,
        const char *output_path,
        int tiling_scale,
        const char *mask_image_path);

    /**
     * 图像平铺
     * @param image_path 图像路径
     * @param tile_multiplier 平铺倍数 (1-10)
     * @param output_path 输出路径
     * @return 错误码，FFI_SUCCESS表示成功
     */
    FFI_EXPORT int tile_image(
        const char *image_path,
        int tile_multiplier,
        const char *output_path);

    /**
     * 图像裁剪（去除透明边缘）
     * @param input_path 输入图像路径
     * @param output_path 输出路径
     * @return 错误码，FFI_SUCCESS表示成功
     */
    FFI_EXPORT int trim_image(
        const char *input_path,
        const char *output_path);

    /**
     * 清理缓存
     * @return 错误码，FFI_SUCCESS表示成功
     */
    FFI_EXPORT int clear_cache();

#ifdef __cplusplus
}
#endif

#endif // IMAGE_BLEND_FFI_H
